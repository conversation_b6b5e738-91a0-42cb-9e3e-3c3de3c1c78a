package com.iot.dashboard.web.response;

import com.iot.dashboard.MeasurementDimension;
import lombok.Getter;
import lombok.Setter;
import com.iot.core.metadata.ConfigMetadata;
import com.iot.core.metadata.DataType;

@Getter
@Setter
public class DimensionInfo {
    private String id;

    private String name;

    private DataType type;

    private ConfigMetadata params;

    private boolean realTime;

    public static DimensionInfo of(MeasurementDimension dimension) {
        DimensionInfo dimensionInfo = new DimensionInfo();
        dimensionInfo.setId(dimension.getDefinition().getId());
        dimensionInfo.setName(dimension.getDefinition().getName());
        dimensionInfo.setParams(dimension.getParams());
        dimensionInfo.setType(dimension.getValueType());
        dimensionInfo.setRealTime(dimension.isRealTime());
        return dimensionInfo;
    }
}
