package com.iot.clickhouse;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.hswebframework.ezorm.rdb.metadata.RDBColumnMetadata;

import java.sql.JDBCType;
import java.sql.SQLType;
import java.time.LocalDate;
import java.time.LocalDateTime;

@AllArgsConstructor
@Getter
public enum SimpleDataType implements ClickHouseDataType {
    Int8(Byte.class, JDBCType.TINYINT),
    Int16(Short.class, JDBCType.SMALLINT),
    Int32(Integer.class, JDBCType.INTEGER),
    Int64(Long.class, JDBCType.BIGINT),

    Float32(Float.class, JDBCType.DECIMAL),
    Float64(Double.class, JDBCType.DECIMAL),

    UUID(java.lang.String.class, JDBCType.VARCHAR),
    FixedString(java.lang.String.class, JDBCType.VARCHAR),
    String(java.lang.String.class, JDBCType.VARCHAR),

    Date(LocalDate.class, JDBCType.DATE),
    DatTime(LocalDateTime.class, JDBCType.TIMESTAMP);

    private final Class<?> javaType;
    private final SQLType sqlType;

    @Override
    public java.lang.String getId() {
        return name();
    }

    @Override
    public java.lang.String getName() {
        return name();
    }

    public java.lang.String createColumnDataType(RDBColumnMetadata columnMetaData) {
        return name();
    }
}
