package com.iot.dashboard;

import com.iot.core.metadata.ConfigMetadata;
import com.iot.core.metadata.DataType;
import org.reactivestreams.Publisher;

/**
 * 指标维度,如: 每小时,服务器1
 * <AUTHOR>
 */
public interface MeasurementDimension {

    DimensionDefinition getDefinition();

    DataType getValueType();

    ConfigMetadata getParams();

    boolean isRealTime();

    Publisher<? extends MeasurementValue> getValue(MeasurementParameter parameter);

}
