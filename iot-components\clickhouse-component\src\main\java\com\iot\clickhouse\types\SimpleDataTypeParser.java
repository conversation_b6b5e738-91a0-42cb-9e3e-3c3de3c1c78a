package com.iot.clickhouse.types;

import com.iot.clickhouse.ClickHouseDataType;
import com.iot.clickhouse.SimpleDataType;


public class SimpleDataTypeParser implements DataTypeParser {
    @Override
    public boolean support(String expr) {
        if (expr.contains("(")) {
            return false;
        }
        try {
            parse(expr);
            return true;
        } catch (Throwable e) {
            return false;
        }
    }

    @Override
    public ClickHouseDataType parse(String expr) {
        return SimpleDataType.valueOf(expr);
    }
}
