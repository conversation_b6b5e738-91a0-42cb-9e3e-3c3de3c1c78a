package com.iot.core.message;

/**
 * 支持回复的消息
 *
 * <AUTHOR>
 * @see com.iot.core.message.property.ReadPropertyMessage
 * @see com.iot.core.message.property.WritePropertyMessage
 * @see com.iot.core.message.function.FunctionInvokeMessage
 * @since 1.0.0
 */
public interface RepayableDeviceMessage<R extends DeviceMessageReply> extends DeviceMessage {

    /**
     * 新建一个回复对象
     *
     * @return 回复对象
     * @see com.iot.core.message.property.ReadPropertyMessageReply
     * @see com.iot.core.message.property.WritePropertyMessageReply
     * @see com.iot.core.message.function.FunctionInvokeMessageReply
     */
    R newReply();

}
