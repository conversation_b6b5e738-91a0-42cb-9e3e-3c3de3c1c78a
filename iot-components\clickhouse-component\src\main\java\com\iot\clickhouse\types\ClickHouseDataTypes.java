package com.iot.clickhouse.types;

import com.iot.clickhouse.ClickHouseDataType;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;

public class ClickHouseDataTypes {

    private final static List<DataTypeParser> parsers = new CopyOnWriteArrayList<>();

    static {
        addParser(new SimpleDataTypeParser());
        addParser(new ArrayDataType.Parser());
    }

    static void addParser(DataTypeParser parser) {
        parsers.add(parser);
    }

    public static Optional<ClickHouseDataType> parse(String expr) {
        for (DataTypeParser parser : parsers) {
            if (parser.support(expr)) {
                return Optional.of(parser.parse(expr));
            }
        }
        return Optional.empty();
    }
}
