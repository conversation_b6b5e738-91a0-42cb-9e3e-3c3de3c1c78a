package com.iot.clickhouse;


import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

public class HttpClickHouseOperationsTest {

    public static void main(String[] args) {
        ClickHouseProperties properties = new ClickHouseProperties();

        RestfulClickHouseOperations operation = new RestfulClickHouseOperations(properties);

        Duration duration = Flux
            .range(0, 1000000)
            .map(i -> {
                Map<String, Object> data = new HashMap<>();
                double val=ThreadLocalRandom.current().nextDouble()*100;
                data.put("id", UUID.randomUUID().toString());
                data.put("timestamp", System.currentTimeMillis());
                data.put("value", String.valueOf(val));
                data.put("numberValue", val);
                data.put("property","temp10");
                return data;
            })
            .as(flux -> operation.insert("properties_demo_device", flux))
            .as(StepVerifier::create)
            .expectComplete()
            .verify();

        System.out.println(duration);
//        operation
//            .query("select * from t where v=10000  limit 1 FORMAT JSON", ResultWrappers.map())
//            .doOnNext(System.out::println)
//            .then()
//            .as(StepVerifier::create)
//            .expectComplete()
//            .verify();
//
//        Duration time = operation
//            .query("desc t", ResultWrappers.map())
//            .doOnNext(System.out::println)
//            .then()
//            .as(StepVerifier::create)
//            .expectComplete()
//            .verify();
//        System.out.println(time);
    }
}