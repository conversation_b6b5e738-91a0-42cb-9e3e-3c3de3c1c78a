package com.iot.clickhouse;

import org.hswebframework.ezorm.rdb.executor.wrapper.ResultWrapper;
import org.springframework.core.io.buffer.DataBuffer;
import reactor.core.publisher.Flux;

import javax.annotation.Nonnull;
import java.util.Optional;

/**
 * @see DefaultFormat
 */
public interface Format {

    @Nonnull
    String getId();

  <R>  Flux<R> parse(Flux<DataBuffer> result, ResultWrapper<R,?> wrapper);

    static Optional<Format> lookup(String format){
        return DefaultFormat.of(format);
    }
}
