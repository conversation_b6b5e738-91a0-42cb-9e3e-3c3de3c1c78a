package com.iot.clickhouse;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.StringUtils;
import org.springframework.util.unit.DataSize;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;

@Getter
@Setter
@ConfigurationProperties(prefix = "clickhouse")
public class ClickHouseProperties {

    private int maxBatchSize = 10000;

    private int maxRetry = 3;

    private Duration retryDuration = Duration.ofSeconds(1);

    private Duration batchDuration = Duration.ofSeconds(2);

    public RestfulConnector restful = new RestfulConnector();

    @Getter
    @Setter
    public static class RestfulConnector {

        private String url = "http://127.0.0.1:8123";

        private String username = "default";

        private String password = "jetlinks";

        public WebClient create() {
            WebClient.Builder builder = WebClient.builder();

            return builder
                .baseUrl(url)
                .defaultHeaders(header -> {
                    if (StringUtils.hasText(password)) {
                        header.setBasicAuth(username, password);
                    }
                })
                .codecs(clientCodecConfigurer -> clientCodecConfigurer
                    .defaultCodecs()
                    .maxInMemorySize((int) DataSize.ofMegabytes(200).toBytes()))
                .build()
                ;

        }
    }
}
