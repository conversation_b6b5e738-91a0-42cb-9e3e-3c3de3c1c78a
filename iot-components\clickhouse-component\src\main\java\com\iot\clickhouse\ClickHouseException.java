package com.iot.clickhouse;

import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;

public class ClickHouseException extends RuntimeException {

    public ClickHouseException(String message) {
        super(message);
    }

    public static Mono<ClickHouseException> of(ClientResponse response) {

        return response.bodyToMono(String.class)
                       .map(ClickHouseException::new);
    }

}
