package com.iot.clickhouse.metadata;


import org.hswebframework.ezorm.rdb.metadata.dialect.DefaultDialect;
import com.iot.clickhouse.SimpleDataType;

import java.util.Locale;

public class ClickHouseDialect extends DefaultDialect {

    public ClickHouseDialect() {

        for (SimpleDataType value : SimpleDataType.values()) {
            registerDataType(value.name(), value);
            registerDataType(value.getSqlType().getName().toLowerCase(Locale.ROOT), value);
        }

    }

    @Override
    public String getQuoteStart() {
        return "\"";
    }

    @Override
    public String getQuoteEnd() {
        return "\"";
    }

    @Override
    public boolean isColumnToUpperCase() {
        return false;
    }

    @Override
    public String getId() {
        return "clickhouse";
    }

    @Override
    public String getName() {
        return "ClickHouse";
    }
}
