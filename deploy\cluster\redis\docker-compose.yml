version: '3.7'
services:
  redis6380:
    image: 'redis'
    container_name: redis6380
    command:
      ["redis-server", "/usr/local/etc/redis/redis.conf"]
    volumes:
      - ./6380/redis.conf:/usr/local/etc/redis/redis.conf
      - ./6380/data:/data
    ports:
      - "6380:6380"
      - "16380:16380"
    environment:
      - TZ=Asia/Shanghai
  redis6381:
    image: 'redis'
    container_name: redis6381
    command:
      ["redis-server", "/usr/local/etc/redis/redis.conf"]
    volumes:
      - ./6381/redis.conf:/usr/local/etc/redis/redis.conf
      - ./6381/data:/data
    ports:
      - "6381:6381"
      - "16381:16381"
    environment:
      - TZ=Asia/Shanghai
  redis6382:
    image: 'redis'
    container_name: redis6382
    command:
      ["redis-server", "/usr/local/etc/redis/redis.conf"]
    volumes:
      - ./6382/redis.conf:/usr/local/etc/redis/redis.conf
      - ./6382/data:/data
    ports:
      - "6382:6382"
      - "16382:16382"
    environment:
      - TZ=Asia/Shanghai