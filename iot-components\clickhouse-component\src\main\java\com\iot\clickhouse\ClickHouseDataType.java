package com.iot.clickhouse;

import org.hswebframework.ezorm.rdb.metadata.dialect.DataTypeBuilder;
import com.iot.clickhouse.types.ClickHouseDataTypes;

import java.util.Optional;

public interface ClickHouseDataType extends org.hswebframework.ezorm.rdb.metadata.DataType, DataTypeBuilder {

    static Optional<ClickHouseDataType> lookup(String expression) {
        return ClickHouseDataTypes.parse(expression);
    }

}
