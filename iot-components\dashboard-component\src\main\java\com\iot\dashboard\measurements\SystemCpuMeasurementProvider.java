package com.iot.dashboard.measurements;

import com.iot.dashboard.*;
import com.iot.dashboard.supports.StaticMeasurement;
import com.iot.dashboard.supports.StaticMeasurementProvider;
import org.hswebframework.utils.time.DateFormatter;
import com.iot.dashboard.*;
import com.iot.core.metadata.ConfigMetadata;
import com.iot.core.metadata.DataType;
import com.iot.core.metadata.types.DoubleType;
import com.iot.core.metadata.unit.UnifyUnit;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Date;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * 实时CPU 使用率监控
 * <pre>
 *     /dashboard/systemMonitor/cpu/usage/realTime
 * </pre>
 *
 * <AUTHOR>
 */
@Component
public class SystemCpuMeasurementProvider
    extends StaticMeasurementProvider {

    public SystemCpuMeasurementProvider() {
        super(DefaultDashboardDefinition.systemMonitor, MonitorObjectDefinition.cpu);
        addMeasurement(cpuUseAgeMeasurement);
    }

    static DataType type = new DoubleType().scale(1).min(0).max(100).unit(UnifyUnit.percent);

    static StaticMeasurement cpuUseAgeMeasurement = new StaticMeasurement(CommonMeasurementDefinition.usage)
        .addDimension(new CpuRealTimeMeasurementDimension());


    static class CpuRealTimeMeasurementDimension implements MeasurementDimension {

        @Override
        public DimensionDefinition getDefinition() {
            return CommonDimensionDefinition.realTime;
        }

        @Override
        public DataType getValueType() {
            return type;
        }

        @Override
        public ConfigMetadata getParams() {
            return null;
        }

        @Override
        public boolean isRealTime() {
            return true;
        }

        @Override
        public Flux<MeasurementValue> getValue(MeasurementParameter parameter) {
            //每秒获取系统CPU使用率
            return Flux.interval(Duration.ofSeconds(1))
                .map(t -> SimpleMeasurementValue.of(BigDecimal
                        .valueOf(SystemMonitor.systemCpuUsage.getValue())
                        .setScale(1, ROUND_HALF_UP),
                    DateFormatter.toString(new Date(), "HH:mm:ss"),
                    System.currentTimeMillis()))
                .cast(MeasurementValue.class);
        }

    }

}
