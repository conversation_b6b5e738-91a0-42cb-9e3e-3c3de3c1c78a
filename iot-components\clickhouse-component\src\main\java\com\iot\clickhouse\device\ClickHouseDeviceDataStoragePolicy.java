package com.iot.clickhouse.device;

import org.apache.commons.collections4.CollectionUtils;
import org.hswebframework.ezorm.core.param.TermType;
import org.hswebframework.ezorm.rdb.executor.SqlRequest;
import org.hswebframework.ezorm.rdb.executor.wrapper.ResultWrappers;
import org.hswebframework.ezorm.rdb.metadata.RDBDatabaseMetadata;
import org.hswebframework.ezorm.rdb.metadata.RDBTableMetadata;
import org.hswebframework.ezorm.rdb.operator.DatabaseOperator;
import org.hswebframework.ezorm.rdb.operator.DefaultDatabaseOperator;
import org.hswebframework.ezorm.rdb.operator.builder.fragments.SimpleTermsFragmentBuilder;
import org.hswebframework.ezorm.rdb.operator.dml.query.Selects;
import org.hswebframework.web.api.crud.entity.PagerResult;
import org.hswebframework.web.api.crud.entity.QueryParamEntity;
import com.iot.core.device.DeviceOperator;
import com.iot.core.device.DeviceProductOperator;
import com.iot.core.device.DeviceRegistry;
import com.iot.core.message.DeviceMessage;
import com.iot.core.metadata.ConfigMetadata;
import com.iot.core.metadata.DeviceMetadata;
import com.iot.core.metadata.PropertyMetadata;
import com.iot.core.metadata.types.GeoPoint;
import com.iot.ValueObject;
import com.iot.clickhouse.ClickHouseOperations;
import com.iot.clickhouse.metadata.ClickHouseDialect;
import com.iot.clickhouse.metadata.ClickHouseReactiveSqlExecutor;
import com.iot.clickhouse.metadata.ClickHouseSchemaMetadata;
import com.iot.device.entity.DeviceProperty;
import com.iot.device.service.data.AbstractDeviceDataStoragePolicy;
import com.iot.device.service.data.DeviceDataService;
import com.iot.device.service.data.DeviceDataStorageProperties;
import com.iot.timeseries.TimeSeriesData;
import com.iot.timeseries.query.Aggregation;
import com.iot.timeseries.query.AggregationData;
import com.iot.reactor.ql.utils.CastUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.function.Function;

/**
 * 使用ClickHouse来存储设备数据,一个属性为一行数据(行式存储)
 * <p>
 * 发布产品时,会自动创建表:properties_{产品ID}和device_log_{产品ID}.
 *
 * <AUTHOR>
 * @since 1.9
 */
public class ClickHouseDeviceDataStoragePolicy extends AbstractDeviceDataStoragePolicy {

    private final ClickHouseOperations operations;

    private final DatabaseOperator databaseOperator;

    public ClickHouseDeviceDataStoragePolicy(ClickHouseOperations operations,
                                             DeviceRegistry registry,
                                             DeviceDataStorageProperties properties) {
        super(registry, properties);
        this.operations = operations;

        RDBDatabaseMetadata database = new RDBDatabaseMetadata(new ClickHouseDialect());
        database.addFeature(new ClickHouseReactiveSqlExecutor(operations));

        ClickHouseSchemaMetadata schema = new ClickHouseSchemaMetadata(operations.getDatabase());

        database.addSchema(schema);
        database.setCurrentSchema(schema);
        databaseOperator = DefaultDatabaseOperator.of(database);
    }

    @Override
    protected Mono<Void> doSaveData(String metric, TimeSeriesData data) {
        return operations.insert(metric, data.values());
    }

    @Override
    protected Mono<Void> doSaveData(String metric, Flux<TimeSeriesData> data) {
        return data
            .flatMap(timeSeriesData -> doSaveData(metric, timeSeriesData))
            .then();
    }

    @Override
    protected Flux<Tuple2<String, TimeSeriesData>> convertProperties(String productId, DeviceMessage message, Map<String, Object> properties) {
        return convertPropertiesForRowPolicy(productId, message, properties);
    }

    @Override
    protected Map<String, Object> createRowPropertyData(String id,
                                                        long timestamp,
                                                        String deviceId,
                                                        PropertyMetadata property,
                                                        Object value) {
        Map<String, Object> values = super.createRowPropertyData(id, timestamp, deviceId, property, value);

        //时间统一转换为时间戳
        values.compute("timeValue", (key, val) -> {
            if (val == null) {
                return null;
            }
            return CastUtils.castDate(val).getTime();
        });
        //地理位置值存储为数组
        values.compute("geoValue", (key, val) -> {
            if (val == null) {
                return null;
            }
            GeoPoint point = GeoPoint.of(val);

            return Arrays.asList(point.getLon(), point.getLat());
        });

        //默认null
        values.putIfAbsent("numberValue", null);
        values.putIfAbsent("timeValue", null);
        values.putIfAbsent("geoValue", null);
        //以下属性不保存
        values.remove("type");
        values.remove("objectValue");
        values.remove("arrayValue");

        return values;
    }

    @Override
    protected Map<String, Object> createDeviceLogData(String productId, DeviceMessage message) {
        Map<String, Object> data = super.createDeviceLogData(productId, message);
        data.remove("productId");
        return data;
    }

    @Override
    protected <T> Flux<T> doQuery(String metric,
                                  QueryParamEntity paramEntity,
                                  Function<TimeSeriesData, T> mapper) {

        return databaseOperator
            .dml()
            .query(metric)
            .setParam(paramEntity)
            .fetch(ResultWrappers.map())
            .reactive()
            .map(map -> {
                long ts = CastUtils.castNumber(map.get("timestamp")).longValue();
                return mapper.apply(TimeSeriesData.of(ts, map));
            });
    }

    @Override
    protected <T> Mono<PagerResult<T>> doQueryPager(String metric,
                                                    QueryParamEntity paramEntity,
                                                    Function<TimeSeriesData, T> mapper) {
        return Mono
            .zip(
                //count
                databaseOperator
                    .dml()
                    .query(metric)
                    .select(Selects.count1().as("total"))
                    .setParam(paramEntity.clone().noPaging().doNotSort())
                    .fetch(ResultWrappers.map())
                    .reactive()
                    .map(map -> CastUtils.castNumber(map.getOrDefault("total", 0)).intValue())
                    .singleOrEmpty()
                    .defaultIfEmpty(0),
                //select
                this
                    .doQuery(metric, paramEntity, mapper)
                    .collectList(),
                (total, data) -> PagerResult.of(total, data, paramEntity)
            );
    }

    @Override
    public String getId() {
        return "clickhouse";
    }

    @Override
    public String getName() {
        return "ClickHouse-行式存储";
    }

    @Override
    public String getDescription() {
        return "使用ClickHouse存储设备数据,每一个属性为一行数据.";
    }

    @Nonnull
    @Override
    public Mono<ConfigMetadata> getConfigMetadata() {
        return Mono.empty();
    }

    @Nonnull
    @Override
    public Mono<Void> registerMetadata(@Nonnull String productId, @Nonnull DeviceMetadata metadata) {

        String propertiesTable = getPropertyTimeSeriesMetric(productId);

        // TODO: 2021/5/8 分布式表支持,自定义分区规则支持。
        //日志表
        String logTableDDL = "create table if not exists " + getDeviceLogMetric(productId) +
            "\n(\nid String comment 'ID'" +
            "\n,timestamp Int64 comment '上报时间'" +
            "\n,createTime Int64 comment '创建时间'" +
            "\n,content String comment '日志内容'" +
            "\n,deviceId String comment '设备ID'" +
            "\n,messageId String comment '消息ID'" +
            "\n,type String comment '日志类型'" +
            "\n)engine = MergeTree()" +
            "\norder by (deviceId,timestamp)" +
            "\npartition by toDate(timestamp/1000)";//一天一个分区
        //属性表
        String propertiesTableDDL = "create table if not exists " + propertiesTable +
            "\n(\nid String comment 'ID'" +
            "\n,timestamp Int64 comment '上报时间'" +
            "\n,createTime Int64 comment '创建时间'" +
            "\n,timeValue Int64 comment '时间类型值'" +
            "\n,property String comment '属性ID'" +
            "\n,deviceId String comment '设备ID'" +
            "\n,value String comment '值'" +
            "\n,numberValue Float64 comment '数字类型值'" +
            "\n,geoValue Array(Float32) comment '地理位置值'" +
            "\n)engine = MergeTree()" +
            "\norder by (deviceId,property,timestamp)" +
            "\npartition by toDate(timestamp/1000)";//一天一个分区
        return operations
            .execute(propertiesTableDDL)
            .then(operations.execute(logTableDDL))
            .then(reloadMetadata(productId, metadata));
    }

    @Override
    protected String getDeviceLogMetric(String productId) {
        return super.getDeviceLogMetric(productId.replace("-", "_").replace(".", "_").toLowerCase());
    }

    @Override
    protected String getPropertyTimeSeriesMetric(String productId) {
        return super.getPropertyTimeSeriesMetric(productId.replace("-", "_").replace(".", "_").toLowerCase());
    }

    @Nonnull
    @Override
    public Mono<Void> reloadMetadata(@Nonnull String productId, @Nonnull DeviceMetadata metadata) {
        return Mono
            .zip(
                databaseOperator
                    .getMetadata()
                    .getCurrentSchema()
                    .getTableReactive(getPropertyTimeSeriesMetric(productId)),
                databaseOperator
                    .getMetadata()
                    .getCurrentSchema()
                    .getTableReactive(getDeviceLogMetric(productId))
            )
            .then();
    }

    @Nonnull
    @Override
    public Flux<DeviceProperty> queryEachOneProperties(@Nonnull String deviceId,
                                                       @Nonnull QueryParamEntity query,
                                                       @Nonnull String... properties) {

        return this
            .getProductAndMetadataByDevice(deviceId)
            .flatMapMany(tp2 -> {
                DeviceMetadata metadata = tp2.getT2();
                Set<String> props = new HashSet<>(Arrays.asList(properties));
                return Flux
                    .fromIterable(metadata.getProperties())
                    .filter(prop -> props.isEmpty() || props.contains(prop.getId()))
                    .flatMap(prop -> this
                        .queryProperty(tp2,
                                       query
                                           .clone()
                                           .doPaging(0, 1)
                                           .and("deviceId", TermType.eq, deviceId),
                                       prop.getId()));
            });
    }

    @Nonnull
    @Override
    public Flux<DeviceProperty> queryEachProperties(@Nonnull String deviceId,
                                                    @Nonnull QueryParamEntity query,
                                                    @Nonnull String... property) {
        Set<String> properties = new HashSet<>(Arrays.asList(property));
        return this
            .getProductAndMetadataByDevice(deviceId)
            .flatMapMany(tp2 -> {
                DeviceMetadata metadata = tp2.getT2();
                return Flux
                    .fromIterable(metadata.getProperties())
                    .filter(prop -> properties.isEmpty() || properties.contains(prop.getId()))
                    .map(prop -> this.queryProperty(tp2,
                                                    query.clone().and("deviceId", TermType.eq, deviceId),
                                                    prop.getId()))
                    .buffer(20)
                    .concatMap(Flux::merge);
            });
    }

    @Nonnull
    @Override
    public Flux<DeviceProperty> queryProperty(@Nonnull String deviceId,
                                              @Nonnull QueryParamEntity query,
                                              @Nonnull String... properties) {
        return this
            .getProductAndMetadataByDevice(deviceId)
            .flatMapMany(tp2 -> queryProperty(tp2, query.clone().and("deviceId", TermType.eq, deviceId), properties));

    }

    private Flux<DeviceProperty> queryProperty(Tuple2<DeviceProductOperator, DeviceMetadata> tp2,
                                               QueryParamEntity query,
                                               String... properties) {
        String table = getPropertyTimeSeriesMetric(tp2.getT1().getId());
        DeviceMetadata metadata = tp2.getT2();
        return query
            .clone()
            .toQuery()
            //指定了属性，则按指定的属性数量进行分页
            .when(properties.length > 0, q -> q
                .doPaging(query.getPageIndex(), properties.length * query.getPageSize())
                .in("property", (Object[]) properties))
            //没有指定属性，则按全部属性数量进行分页
            .when(properties.length == 0, q -> q
                .doPaging(query.getPageIndex(), metadata.getProperties().size() * query.getPageSize()))
            .when(CollectionUtils.isEmpty(query.getSorts()), q -> q.orderByDesc("timestamp"))
            .execute(databaseOperator.dml().query(table)::setParam)
            .fetch(ResultWrappers.map())
            .reactive()
            .map(map -> {
                String property = String.valueOf(map.get("property"));
                long timestamp = CastUtils.castNumber(map.get("timestamp")).longValue();

                return DeviceProperty
                    .of(TimeSeriesData.of(timestamp, map), metadata.getPropertyOrNull(property));
            });
    }

    @Nonnull
    @Override
    public Flux<DeviceProperty> queryPropertyByProductId(@Nonnull String productId,
                                                         @Nonnull QueryParamEntity query,
                                                         @Nonnull String... properties) {
        return this
            .getProductAndMetadataByProduct(productId)
            .flatMapMany(tp2 -> queryProperty(tp2, query, properties));
    }


    private String createAggFunction(Aggregation aggregation) {
        switch (aggregation) {
            case COUNT:
                return "count(id)";
            case AVG:
                return "avg(numberValue)";
            case MAX:
                return "max(numberValue)";
            case MIN:
                return "min(numberValue)";
            case SUM:
                return "sum(numberValue)";
            case FIRST:
                return "first_value(numberValue)";
            case MEDIAN:
                return "median(numberValue)";

        }
        throw new UnsupportedOperationException("不支持的聚合函数:" + aggregation);
    }

    @Override
    public Flux<AggregationData> aggregationPropertiesByProduct(@Nonnull String productId,
                                                                @Nonnull DeviceDataService.AggregationRequest request,
                                                                @Nonnull DeviceDataService.DevicePropertyAggregation... properties) {
        Map<Long, Map<String, Object>> dataReference = aggCompleteResultReference(request, properties);
        String table = getPropertyTimeSeriesMetric(productId);
        boolean groupByTime = request.getInterval() != null;

        StringBuilder sql = new StringBuilder();
        if (request.getInterval() != null) {
            long interval = request.getInterval().toMillis();
            String SQL = "select round(timestamp/%d)*%d time,property";
            sql.append(String.format(SQL, interval, interval));
        } else {
            sql.append("select property");
        }
        Set<String> propertyId = new HashSet<>();

        for (DeviceDataService.DevicePropertyAggregation property : properties) {
            sql.append(",if(property='").append(property.getProperty()).append("',");
            sql
                .append(createAggFunction(property.getAgg()))
                .append(",null) \"")
                .append(property.getAlias())
                .append("\"");
            propertyId.add(property.getProperty());
        }
        if (!propertyId.isEmpty()) {
            request.getFilter().and("property", TermType.in, propertyId);
        }
        request.getFilter().and("timestamp", TermType.btw, Arrays.asList(request.getFrom(), request.getTo()));

        sql.append(" from ").append(table);

        return databaseOperator
            .getMetadata()
            .getTableOrViewReactive(table)
            .cast(RDBTableMetadata.class)
            .flatMapMany(tableMeta -> {
                SqlRequest sqlRequest = SimpleTermsFragmentBuilder
                    .instance()
                    .createTermFragments(tableMeta, request.getFilter().getTerms())
                    .toRequest();
                if (sqlRequest.isNotEmpty()) {
                    sql.append(" where ").append(sqlRequest.toNativeSql());
                }
                sql.append(" group by property");
                if (groupByTime) {
                    sql.append(",time");
                }
                return operations
                    .query(sql.toString(), ResultWrappers.map())
                    .map(ValueObject::of);
            })
            //按时间分组
            .groupBy(value -> value.getLong("time").orElse(0L))
            .flatMap(group -> group
                .doOnNext(value -> {
                    for (DeviceDataService.DevicePropertyAggregation property : properties) {
                        value.get(property.getAlias())
                             .ifPresent(val -> {
                                 Optional.ofNullable(dataReference.get(group.key()))
                                         .ifPresent(data -> data.put(property.getAlias(), val));
                             });
                    }
                })
            )
            .thenMany(Flux.fromIterable(dataReference.values()))
            .map(AggregationData::of);
    }


    /**
     * 获取完整数据参考
     */
    private Map<Long, Map<String, Object>> aggCompleteResultReference(DeviceDataService.AggregationRequest request,
                                                                      DeviceDataService.DevicePropertyAggregation... properties) {
        DateTimeFormatter formatter = DateTimeFormat.forPattern(request.getFormat());
        Assert.notNull(request.getInterval(), "聚合时间间隔不能为空");
        long interval = request.getInterval().toMillis();
        Map<Long, Map<String, Object>> data = new LinkedHashMap<>();
        Map<String, Object> valueMap = new HashMap<>();
        long endTimestamp = (request.getTo().getTime() / interval) * interval;
        for (DeviceDataService.DevicePropertyAggregation property : properties) {
            valueMap.put(property.getAlias(), 0);
        }
        // TODO: 2021/5/20 加入排序后，处理逻辑将变更
        //默认倒叙排列
        for (long i = endTimestamp; i > request.getFrom().getTime(); i -= interval) {
            String formatValue = new DateTime(new Date(i)).toString(formatter);
            Map<String, Object> tempData = new HashMap<>(valueMap);
            tempData.put("time", formatValue);
            data.put(i, tempData);
            if (request.getLimit() == data.size()) {
                return data;
            }
        }
        return data;
    }

    private String toCHFormat(String format) {
        return format
            .replace("yyyy", "%Y") //年
            .replace("MM", "%m") //月
            .replace("dd", "%d")//日
            .replace("HH", "%H")//时
            .replace("mm", "%M")//分
            .replace("ss", "%S")//秒
            ;
    }

    @Override
    public Flux<AggregationData> aggregationPropertiesByDevice(@Nonnull String deviceId,
                                                               @Nonnull DeviceDataService.AggregationRequest request,
                                                               @Nonnull DeviceDataService.DevicePropertyAggregation... properties) {
        return deviceRegistry
            .getDevice(deviceId)
            .flatMap(DeviceOperator::getProduct)
            .flatMapMany(product -> {
                DeviceDataService.AggregationRequest newRequest = request.copy();
                newRequest.getFilter().and("deviceId", TermType.eq, deviceId);
                return aggregationPropertiesByProduct(product.getId(), newRequest, properties);
            });
    }

    @Nonnull
    @Override
    public Mono<PagerResult<DeviceProperty>> queryPropertyPage(@Nonnull String deviceId,
                                                               @Nonnull String property,
                                                               @Nonnull QueryParamEntity param) {
        return getProductAndMetadataByDevice(deviceId)
            .flatMap(productAndMetadata -> queryPropertyPage(productAndMetadata, param.and("deviceId", TermType.eq, deviceId), property));
    }


    @Nonnull
    @Override
    public Mono<PagerResult<DeviceProperty>> queryPropertyPageByProductId(@Nonnull String productId,
                                                                          @Nonnull String property,
                                                                          @Nonnull QueryParamEntity query) {
        return getProductAndMetadataByProduct(productId)
            .flatMap(productAndMetadata -> queryPropertyPage(productAndMetadata, query, property));
    }


    @Nonnull
    @Override
    public Mono<PagerResult<DeviceProperty>> queryPropertyPage(@Nonnull String deviceId,
                                                               @Nonnull QueryParamEntity query,
                                                               @Nonnull String... properties) {
        return getProductAndMetadataByDevice(deviceId)
            .flatMap(productAndMetadata -> queryPropertyPage(productAndMetadata, query.and("deviceId", TermType.eq, deviceId), properties));
    }

    @Nonnull
    @Override
    public Mono<PagerResult<DeviceProperty>> queryPropertyPageByProductId(@Nonnull String productId,
                                                                          @Nonnull QueryParamEntity query,
                                                                          @Nonnull String... properties) {
        return getProductAndMetadataByProduct(productId)
            .flatMap(productAndMetadata -> queryPropertyPage(productAndMetadata, query, properties));
    }

    private Mono<PagerResult<DeviceProperty>> queryPropertyPage(@Nonnull Tuple2<DeviceProductOperator, DeviceMetadata> productAndMetadata,
                                                                @Nonnull QueryParamEntity query,
                                                                @Nonnull String... property) {

        String table = getPropertyTimeSeriesMetric(productAndMetadata.getT1().getId());

        return query
            .toQuery()
            .when(property.length > 0, q -> q.in("property", Arrays.asList(property)))
            .execute(param -> this
                .doQueryPager(table, query, data -> DeviceProperty.of(data, data
                    .getString("property")
                    .map(productAndMetadata.getT2()::getPropertyOrNull)
                    .orElse(null)
                )));
    }
}
