package com.iot.elastic.search.aggreation.metrics;

import org.hswebframework.utils.StringUtils;
import com.iot.elastic.search.aggreation.enums.MetricsType;
import lombok.*;

/**
 * <AUTHOR>
 * @since 1.0
 **/
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetricsAggregationStructure {

    @NonNull
    private String field;

    private String name;

    @NonNull
    private MetricsType type = MetricsType.COUNT;

    /**
     * 缺失值
     */
    private Object missingValue;

    public String getName() {
        if (StringUtils.isNullOrEmpty(name)) {
            name = type.name().concat("_").concat(field);
        }
        return name;
    }
}
