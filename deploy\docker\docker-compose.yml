version: '3'
services:
  ironman-iot:
    image: "${REGISTER}/iot/ironman-iot:${TAG}"
    ports:
      - 8180:8180 # API端口
      - 1883:1883 # MQTT端口
      - 11883:11883 # 通过openAPI使用mqtt订阅平台消息
      #- 8100-8110:8100-8110 # 预留端口
      #- 8200-8210:8200-8210/udp # udp端口
    volumes:
      - "./data/upload:/ironman/static/upload"  # 持久化上传的文件
      - "./data/protocols:/ironman/data/protocols" # 临时保存协议目录
    environment:
      - TZ=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=${ACTIVE}
    restart: always


