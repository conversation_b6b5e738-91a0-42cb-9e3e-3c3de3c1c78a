package com.iot.elastic.search.index.mapping;

import com.iot.elastic.search.enums.ElasticDateFormat;
import com.iot.elastic.search.enums.ElasticPropertyType;
import lombok.*;

/**
 * <AUTHOR>
 * @since 1.0
 **/
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SingleMappingMetadata {

    private String name;

    private ElasticDateFormat format;

    private ElasticPropertyType type;
}
