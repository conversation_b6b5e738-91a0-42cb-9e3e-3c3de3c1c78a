package com.iot.clickhouse.configuration;

import com.iot.core.device.DeviceRegistry;
import com.iot.clickhouse.ClickHouseOperations;
import com.iot.clickhouse.ClickHouseProperties;
import com.iot.clickhouse.RestfulClickHouseOperations;
import com.iot.clickhouse.device.ClickHouseDeviceDataStoragePolicy;
import com.iot.device.service.data.DeviceDataStoragePolicy;
import com.iot.device.service.data.DeviceDataStorageProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(ClickHouseProperties.class)
@ConditionalOnProperty(prefix = "clickhouse", value = "enabled", havingValue = "true")
public class ClickHouseConfiguration {


    @Bean(destroyMethod = "destroy")
    public RestfulClickHouseOperations clickHouseOperations(ClickHouseProperties properties) {
        return new RestfulClickHouseOperations(properties);
    }


    @Bean
    @ConditionalOnBean(DeviceRegistry.class)
    public DeviceDataStoragePolicy clickHouseDeviceDataStoragePolicy(ClickHouseOperations operations,
                                                                     DeviceRegistry registry,
                                                                     DeviceDataStorageProperties properties) {
        return new ClickHouseDeviceDataStoragePolicy(operations, registry, properties);
    }
}
