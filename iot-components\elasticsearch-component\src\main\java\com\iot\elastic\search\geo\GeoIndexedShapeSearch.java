package com.iot.elastic.search.geo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import com.iot.geo.GeoRelation;
import com.iot.geo.GeoSearch;

@Getter
@Setter
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
public class GeoIndexedShapeSearch implements GeoSearch {
    private String index;

    private String id;

    private String path;

    private GeoRelation relation;

}
