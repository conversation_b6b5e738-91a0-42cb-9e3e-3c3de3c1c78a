package com.iot.external;

import net.sf.jsqlparser.expression.Function;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.statement.select.PlainSelect;
import com.iot.reactor.ql.ReactorQLContext;
import com.iot.reactor.ql.ReactorQLRecord;
import com.iot.reactor.ql.supports.DefaultReactorQLMetadata;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class SpelFunctionFeatureTest {

    @Test
    void test() {
        doTest("#value", Collections.singletonMap("value",123),123);
        doTest("#value+1",Collections.singletonMap("value",123),124);
        doTest("#value.doubleValue()",Collections.singletonMap("value",123),123D);

        doTest("#value[0]",Collections.singletonMap("value", Collections.singletonList(123)),123);


    }

    void doTest(String expr, Map<String, Object> val, Object expect) {
        SpelFunctionFeature feature = new SpelFunctionFeature();
        Function function = new Function();
        function.setParameters(new ExpressionList(Arrays.asList(new StringValue(expr))));
        Mono.from(feature
            .createMapper(function, new DefaultReactorQLMetadata(new PlainSelect()))
            .apply(ReactorQLRecord.newRecord(null, val, ReactorQLContext.ofDatasource(id -> Mono.empty())))
        ).cast(Object.class)
            .as(StepVerifier::create)
            .expectNext(expect)
            .verifyComplete()

        ;
    }

}